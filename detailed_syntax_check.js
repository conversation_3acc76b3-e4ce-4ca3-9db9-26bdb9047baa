// 详细的语法检查脚本
const fs = require('fs');

function checkJavaScriptSyntax(filePath) {
    try {
        console.log(`正在检查文件: ${filePath}`);
        
        // 读取文件
        const code = fs.readFileSync(filePath, 'utf8');
        console.log(`文件大小: ${code.length} 字符`);
        console.log(`文件行数: ${code.split('\n').length} 行`);
        
        // 尝试解析JavaScript代码
        new Function(code);
        
        console.log('✅ JavaScript语法检查通过！');
        console.log('✅ 所有语法错误已修复');
        console.log('✅ 文件现在应该可以在JSNice工具中正常反编译');
        
        return true;
    } catch (error) {
        console.error('❌ JavaScript语法错误：');
        console.error(error.message);
        
        // 提取行号信息
        const match = error.message.match(/line (\d+)/i);
        if (match) {
            const lineNumber = parseInt(match[1]);
            console.log(`错误位置：第 ${lineNumber} 行`);
            
            // 显示错误行周围的代码
            const lines = fs.readFileSync(filePath, 'utf8').split('\n');
            const start = Math.max(0, lineNumber - 3);
            const end = Math.min(lines.length, lineNumber + 2);
            
            console.log('\n错误行周围的代码:');
            for (let i = start; i < end; i++) {
                const marker = i === lineNumber - 1 ? '>>> ' : '    ';
                console.log(`${marker}${i + 1}: ${lines[i]}`);
            }
        }
        
        return false;
    }
}

// 检查bundle_1.js
const result = checkJavaScriptSyntax('bundle_1.js');

if (result) {
    console.log('\n🎉 修复完成！');
    console.log('现在你可以将bundle_1.js文件上传到JSNice工具进行反编译了。');
} else {
    console.log('\n❌ 仍有语法错误需要修复。');
}
